import type { Budget } from "../types";

import { PlusIcon } from "lucide-react";

import { Button } from "~/components/ui/button";

import { useBudgetActions } from "../hooks/use-budget-actions";
import BudgetsListItem from "./budgets-list-item";

interface Props {
  budgets: Budget[];
}

export default function BudgetsList({ budgets }: Props) {
  const { createBudget } = useBudgetActions();

  const activeBudgets = budgets.filter((budget) => budget.is_active);
  const inactiveBudgets = budgets.filter((budget) => !budget.is_active);

  if (budgets.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="space-y-4">
          <p className="text-muted-foreground">No budgets found</p>
          <Button onClick={() => createBudget()}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create Your First Budget
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {activeBudgets.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Active Budgets</h2>
          <div className="space-y-4">
            {activeBudgets.map((budget) => (
              <BudgetsListItem key={budget.id} budget={budget} />
            ))}
          </div>
        </div>
      )}

      {inactiveBudgets.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Inactive Budgets</h2>
          <div className="space-y-4">
            {inactiveBudgets.map((budget) => (
              <BudgetsListItem key={budget.id} budget={budget} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
