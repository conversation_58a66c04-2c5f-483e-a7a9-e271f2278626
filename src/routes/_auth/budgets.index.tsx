import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import { Button } from "~/components/ui/button";
import { BudgetDialog, BudgetsList } from "~/features/budgets/components";
import { useBudgets } from "~/features/budgets/hooks";
import useBudgetsStore from "~/features/budgets/store";

export const Route = createFileRoute("/_auth/budgets/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgets, isLoading, error } = useBudgets();

  const { setDialogOpen, setDialogMode, setCurrentBudget, setInitialValues } = useBudgetsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentBudget: state.setCurrentBudget,
      setInitialValues: state.setInitialValues,
    }))
  );

  const handleCreateBudget = () => {
    setCurrentBudget(undefined);
    setInitialValues(undefined);
    setDialogMode("create");
    setDialogOpen(true);
  };

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budgets" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budgets" />
        <ErrorMessage title="Can't load budgets" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Budgets">
        <Button onClick={handleCreateBudget}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Create Budget
        </Button>
      </PageHeader>

      <BudgetsList budgets={budgets} />
      <BudgetDialog />
    </>
  );
}
