import { z } from "zod";

import { decimal, nullableString } from "~/lib/schemas";

export const periodTypes = ["week", "month", "quarter", "year"] as const;

export const periodTypesMap: Record<(typeof periodTypes)[number], string> = {
  week: "Week",
  month: "Month", 
  quarter: "Quarter",
  year: "Year",
};

export const BudgetCreateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: nullableString(),
  period_type: z.enum(periodTypes),
  is_percentage: z.boolean(),
  amount: decimal()
    .refine((value) => Number(value) >= 0.01, {
      message: "Amount must be at least 0.01",
    })
    .refine(
      (value, ctx) => {
        if (ctx.parent.is_percentage && Number(value) > 100) {
          return false;
        }
        return true;
      },
      { message: "Percentage cannot exceed 100%" }
    ),
});

export const BudgetUpdateSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: nullableString(),
  amount: decimal().refine((value) => Number(value) >= 0.01, {
    message: "Amount must be at least 0.01",
  }),
  is_active: z.boolean(),
});
