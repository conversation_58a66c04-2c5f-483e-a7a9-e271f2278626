import type { Budget } from "../types";

import { useMemo } from "react";

import { Link } from "@tanstack/react-router";
import { EditIcon, MoreHorizontalIcon, TrashIcon, ViewIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Progress } from "~/components/ui/progress";

import { useBudgetActions } from "../hooks";
import { periodTypesMap } from "../schemas";

interface Props {
  budget: Budget;
}

export default function BudgetsListItem({ budget }: Props) {
  const { editBudget, deleteBudget } = useBudgetActions(budget);

  const usagePercentage = useMemo(() => {
    const planned = Number(budget.current_period.planned_amount);
    const used = Number(budget.current_period.used_amount);

    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [budget.current_period]);

  return (
    <div className="bg-card rounded-lg border p-4">
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <Link to="/budgets/$budgetId" params={{ budgetId: budget.id }} className="link text-base font-medium">
              {budget.name}
            </Link>
            {!budget.is_active && (
              <span className="bg-muted text-muted-foreground rounded-full px-2 py-1 text-xs">Inactive</span>
            )}
          </div>

          {budget.description && <p className="text-muted-foreground text-sm">{budget.description}</p>}

          <div className="text-muted-foreground flex items-center gap-4 text-sm">
            <span>{periodTypesMap[budget.period_type]}</span>
            <span>{budget.is_percentage ? `${budget.amount}%` : `$${budget.amount}`}</span>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current Period Usage</span>
              <span>
                ${budget.current_period.used_amount} / ${budget.current_period.planned_amount}
              </span>
            </div>
            <Progress value={usagePercentage} className="h-2" />
            <div className="text-muted-foreground text-xs">{usagePercentage.toFixed(1)}% used</div>
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontalIcon />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link to="/budgets/$budgetId" params={{ budgetId: budget.id }}>
                <ViewIcon />
                <span className="inline-block pt-0.5">Budget details</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={editBudget}>
              <EditIcon />
              <span className="inline-block pt-0.5">Edit budget</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={deleteBudget} variant="destructive">
              <TrashIcon />
              <span className="inline-block pt-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
