# Feature Request: Budgets Management

## Overview

This document outlines the implementation of a comprehensive budgets management feature for the Finanze.Pro application. The feature will allow users to create, manage, and track budgets with support for both fixed amounts and percentage-based budgets.

## Feature Description

The budgets feature enables users to:

- Create and manage multiple budgets
- Set budgets as fixed amounts or percentage of income
- Track budget usage for current periods
- View historical budget data
- Enable/disable budgets as needed

## Technical Requirements

### Base Types (Already Defined)

The base types are already defined in `src/features/budgets/types.ts`:

```typescript
export type PeriodType = "week" | "month" | "quarter" | "year";

export interface BudgetPeriod {
  id: string;
  period_start: string;
  period_end: string;
  planned_amount: string;
  used_amount: string;
  created_at: string;
  updated_at: string;
}

export interface Budget {
  id: string;
  name: string;
  description: string | null;
  period_type: PeriodType;
  is_percentage: boolean;
  amount: string;
  current_period: BudgetPeriod;
  is_active: boolean;
  included_accounts: string[];
  created_at: string;
  updated_at: string;
}
```

### API Endpoints

Following the existing API patterns (`/v1/` prefix):

#### Budget CRUD Operations

- `GET /v1/budgets` - List all budgets
- `POST /v1/budgets` - Create new budget
- `GET /v1/budgets/{budgetId}` - Get specific budget
- `PUT /v1/budgets/{budgetId}` - Update budget
- `DELETE /v1/budgets/{budgetId}` - Delete budget

#### Budget History

- `GET /v1/budgets/{budgetId}/history` - Get paginated historical records

### Validation Schema

Following the existing schema patterns in `src/features/budgets/schemas.ts`:

```typescript
export const periodTypes = ["week", "month", "quarter", "year"] as const;

export const BudgetCreateSchema = z.object({
  name: z.string().min(3).max(50),
  description: nullableString(),
  period_type: z.enum(periodTypes),
  is_percentage: z.boolean(),
  amount: decimal()
    .refine((value) => Number(value) >= 0.01, {
      message: "Amount must be at least 0.01",
    })
    .refine(
      (value, ctx) => {
        if (ctx.parent.is_percentage && Number(value) > 100) {
          return false;
        }
        return true;
      },
      { message: "Percentage cannot exceed 100%" }
    ),
});

export const BudgetUpdateSchema = z.object({
  name: z.string().min(3).max(50),
  description: nullableString(),
  amount: decimal().refine((value) => Number(value) >= 0.01, {
    message: "Amount must be at least 0.01",
  }),
  is_active: z.boolean(),
});
```

### Store Implementation

Following the Zustand pattern used in other features:

```typescript
interface State {
  dialogOpen: boolean;
  dialogMode: "create" | "edit" | "delete";
  currentBudget?: Budget;
  initialValues?: Partial<BudgetCreateData>;
}

interface Actions {
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentBudget: (budget?: Budget) => void;
  setInitialValues: (initialValues?: Partial<BudgetCreateData>) => void;
  closeDialog: () => void;
}
```

### Hooks Implementation

Following the existing hook patterns:

#### Core Hooks

- `useBudgets()` - Fetch all budgets
- `useBudget(budgetId)` - Fetch specific budget
- `useBudgetCreate()` - Create budget mutation
- `useBudgetUpdate(budgetId)` - Update budget mutation
- `useBudgetDelete(budgetId)` - Delete budget mutation
- `useBudgetHistory(budgetId, page, per_page)` - Fetch budget history with pagination

### Component Structure

Following the existing component organization patterns:

#### Core Components

- `BudgetDialog` - Main dialog container with mode switching
- `BudgetFormCreate` - Create budget form
- `BudgetFormEdit` - Edit budget form
- `BudgetFormDelete` - Delete confirmation form
- `BudgetsList` - List of budgets with actions
- `BudgetsListItem` - Individual budget item
- `BudgetHistoryList` - Paginated history list
- `BudgetHistoryItem` - Individual history item

### Routing

Following the existing routing structure:

- `/budgets` - Main budgets list page (`src/routes/_auth/budgets.index.tsx`)
- `/budgets/{budgetId}` - Budget details page (`src/routes/_auth/budgets.$budgetId.tsx`)

### UI/UX Requirements

#### Budget List Page

- Page header with "Budgets" title and create button
- List of budgets grouped by active/inactive status
- Each budget item shows:
  - Name and description
  - Period type and amount (with percentage indicator)
  - Current period usage (progress bar)
  - Action buttons (edit, delete, view history)

#### Budget Forms

- Create form fields: name, description, period_type, is_percentage, amount
- Edit form fields: name, description, amount, is_active
- Validation following existing patterns
- Error handling with `ErrorMessage` component

#### Budget Details Page

- Budget information display
- Current period statistics
- Quick actions (edit, delete)
- Historical data section with pagination

#### Amount Input

- Dynamic validation based on `is_percentage` flag
- Show percentage symbol when applicable
- Min value 0.01, max 100 for percentages

### Data Flow

1. **Budget Creation**: Form → Validation → API → Store Update → UI Refresh
2. **Budget Editing**: Load current data → Form → Validation → API → Store Update → UI Refresh
3. **Budget Deletion**: Confirmation → API → Store Update → UI Refresh

### Error Handling

Following existing patterns:

- API errors displayed using `ErrorMessage` component
- Form validation errors shown inline
- Toast notifications for success/error states
- Loading states during API operations

### Testing Considerations

- Unit tests for validation schemas
- Component tests for forms and lists
- Integration tests for API hooks

## Implementation Plan

### Phase 1: Core Structure

1. Create validation schemas
2. Implement store with Zustand
3. Create basic API hooks
4. Set up routing structure

### Phase 2: Components

1. Implement CRUD forms
2. Build budget list components
3. Create dialog containers

### Phase 3: Pages & Integration

1. Implement budget list page
2. Implement budget details page
3. Add navigation integration
4. Implement history functionality

### Phase 4: Polish & Testing

1. Add comprehensive error handling
2. Implement loading states
3. Add toast notifications
4. Write tests
5. UI/UX refinements

## Dependencies

- Existing UI components (Button, Dialog, Form, etc.)
- Existing input components patterns
- API client and query patterns
- Validation utilities (zod, schemas)
- State management (Zustand)
- Routing (TanStack Router)

## Notes

- The feature follows all existing patterns and conventions
- Uses the established component structure and naming
- Maintains consistency with other features (accounts, transactions, categories)
- Leverages existing UI components and patterns
- Follows the established API and validation patterns
