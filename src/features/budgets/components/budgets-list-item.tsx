import type { Budget } from "../types";

import { useMemo } from "react";

import { EditIcon, MoreHorizontalIcon, TrashIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Progress } from "~/components/ui/progress";

import { periodTypesMap } from "../schemas";
import useBudgetsStore from "../store";

interface Props {
  budget: Budget;
}

export default function BudgetsListItem({ budget }: Props) {
  const { setDialogOpen, setDialogMode, setCurrentBudget } = useBudgetsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentBudget: state.setCurrentBudget,
    }))
  );

  const usagePercentage = useMemo(() => {
    const planned = Number(budget.current_period.planned_amount);
    const used = Number(budget.current_period.used_amount);
    
    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [budget.current_period]);

  const handleEdit = () => {
    setCurrentBudget(budget);
    setDialogMode("edit");
    setDialogOpen(true);
  };

  const handleDelete = () => {
    setCurrentBudget(budget);
    setDialogMode("delete");
    setDialogOpen(true);
  };

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <h3 className="font-medium">{budget.name}</h3>
            {!budget.is_active && (
              <span className="rounded-full bg-muted px-2 py-1 text-xs text-muted-foreground">Inactive</span>
            )}
          </div>
          
          {budget.description && (
            <p className="text-sm text-muted-foreground">{budget.description}</p>
          )}

          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{periodTypesMap[budget.period_type]}</span>
            <span>
              {budget.is_percentage ? `${budget.amount}%` : `$${budget.amount}`}
            </span>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current Period Usage</span>
              <span>
                ${budget.current_period.used_amount} / ${budget.current_period.planned_amount}
              </span>
            </div>
            <Progress value={usagePercentage} className="h-2" />
            <div className="text-xs text-muted-foreground">
              {usagePercentage.toFixed(1)}% used
            </div>
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEdit}>
              <EditIcon className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDelete} className="text-destructive">
              <TrashIcon className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
