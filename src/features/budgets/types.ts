export type PeriodType = "week" | "month" | "quarter" | "year";

export interface BudgetPeriod {
  id: string;
  period_start: string;
  period_end: string;
  planned_amount: string;
  used_amount: string;
  created_at: string;
  updated_at: string;
}

export interface Budget {
  id: string;
  name: string;
  description: string | null;
  period_type: PeriodType;
  is_percentage: boolean;
  amount: string;
  current_period: BudgetPeriod;
  is_active: boolean;
  included_accounts: string[];
  created_at: string;
  updated_at: string;
}
