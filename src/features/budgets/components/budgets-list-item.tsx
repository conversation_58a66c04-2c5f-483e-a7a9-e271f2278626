import type { Budget } from "../types";

import { useMemo } from "react";

import { EditIcon, MoreHorizontalIcon, TrashIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Progress } from "~/components/ui/progress";

import { useBudgetActions } from "../hooks";
import { periodTypesMap } from "../schemas";

interface Props {
  budget: Budget;
}

export default function BudgetsListItem({ budget }: Props) {
  const { editBudget, deleteBudget } = useBudgetActions(budget);

  const usagePercentage = useMemo(() => {
    const planned = Number(budget.current_period.planned_amount);
    const used = Number(budget.current_period.used_amount);

    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [budget.current_period]);

  return (
    <div className="bg-card rounded-lg border p-4">
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <h3 className="font-medium">{budget.name}</h3>
            {!budget.is_active && (
              <span className="bg-muted text-muted-foreground rounded-full px-2 py-1 text-xs">Inactive</span>
            )}
          </div>

          {budget.description && <p className="text-muted-foreground text-sm">{budget.description}</p>}

          <div className="text-muted-foreground flex items-center gap-4 text-sm">
            <span>{periodTypesMap[budget.period_type]}</span>
            <span>{budget.is_percentage ? `${budget.amount}%` : `$${budget.amount}`}</span>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current Period Usage</span>
              <span>
                ${budget.current_period.used_amount} / ${budget.current_period.planned_amount}
              </span>
            </div>
            <Progress value={usagePercentage} className="h-2" />
            <div className="text-muted-foreground text-xs">{usagePercentage.toFixed(1)}% used</div>
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontalIcon className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={editBudget}>
              <EditIcon className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={deleteBudget} className="text-destructive">
              <TrashIcon className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
