import { PlusIcon } from "lucide-react";

import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import { Button } from "~/components/ui/button";
import BudgetDialog from "~/features/budgets/components/budget-dialog";
import BudgetsList from "~/features/budgets/components/budgets-list";
import { useBudgetActions, useBudgets } from "~/features/budgets/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgets, isLoading, error } = useBudgets();
  const { createBudget } = useBudgetActions();

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budgets" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budgets" />
        <ErrorMessage title="Can't load budgets" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Budgets">
        <div className="flex items-center justify-between gap-2">
          <div></div>
          <div>
            {budgets.length > 0 && (
              <Button onClick={() => createBudget()}>
                <PlusIcon />
                <span className="inline-block pt-0.5">Create Budget</span>
              </Button>
            )}
          </div>
        </div>
      </PageHeader>

      <BudgetsList budgets={budgets} />
      <BudgetDialog />
    </>
  );
}
