import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import BudgetDetailsOverview from "~/features/budgets/components/budget-details-overview";
import BudgetDialog from "~/features/budgets/components/budget-dialog";
import BudgetHistoryList from "~/features/budgets/components/budget-history-list";
import { useBudget } from "~/features/budgets/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgetId } = Route.useParams();

  const { budget, isLoading, error } = useBudget(budgetId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
        <ErrorMessage title="Can't load budget" error={error} />
      </>
    );
  }

  if (!budget) {
    return (
      <>
        <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
        <ErrorMessage title="Budget not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
      <BudgetDetailsOverview budget={budget} />

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Budget History</h2>
        <BudgetHistoryList budgetId={budgetId} />
      </div>

      <BudgetDialog />
    </>
  );
}
