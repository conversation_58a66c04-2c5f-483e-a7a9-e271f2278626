import type { BudgetPeriod, PaginationMeta } from "../types";

import { useState } from "react";

import PaginationBlock from "~/components/blocks/pagination-block";

import { useBudgetHistory } from "../hooks";
import BudgetHistoryItem from "./budget-history-item";

interface Props {
  budgetId: string;
}

export default function BudgetHistoryList({ budgetId }: Props) {
  const [currentPage, setCurrentPage] = useState(1);
  const perPage = 10;

  const { history, pagination, isLoading, error } = useBudgetHistory({
    budgetId,
    page: currentPage,
    per_page: perPage,
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="animate-pulse rounded-lg border bg-card p-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <div className="h-4 w-32 bg-muted rounded" />
                <div className="h-4 w-24 bg-muted rounded" />
              </div>
              <div className="h-2 bg-muted rounded" />
              <div className="flex justify-between">
                <div className="h-3 w-16 bg-muted rounded" />
                <div className="h-3 w-20 bg-muted rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load budget history</p>
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No budget history found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {history.map((period) => (
          <BudgetHistoryItem key={period.id} period={period} />
        ))}
      </div>

      {pagination && (
        <PaginationBlock
          pagination={pagination}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
}
