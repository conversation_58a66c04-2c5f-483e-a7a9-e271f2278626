import type { BudgetCreateData } from "../types";

import { useMemo } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import InputRadioGroup from "~/components/inputs/input-radio-group";
import InputSelect from "~/components/inputs/input-select";
import InputText from "~/components/inputs/input-text";
import InputTextarea from "~/components/inputs/input-textarea";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";

import { useBudgetCreate } from "../hooks";
import { BudgetCreateSchema, periodTypesMap } from "../schemas";
import useBudgetsStore from "../store";

export default function BudgetFormCreate() {
  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));
  const initialValues = useBudgetsStore(useShallow((state) => state.initialValues));

  const { mutate: createBudget, isPending, error } = useBudgetCreate();

  const periodTypeOptions = useMemo(
    () => Object.entries(periodTypesMap).map(([value, label]) => ({ value, label })),
    []
  );

  const percentageOptions = useMemo(
    () => [
      { value: "false", label: "Fixed Amount" },
      { value: "true", label: "Percentage of Income" },
    ],
    []
  );

  const defaultValues = useMemo<BudgetCreateData>(
    () => ({
      name: initialValues?.name ?? "",
      description: initialValues?.description ?? "",
      period_type: initialValues?.period_type ?? "month",
      is_percentage: initialValues?.is_percentage ?? false,
      amount: initialValues?.amount ?? "0.00",
    }),
    [initialValues]
  );

  const form = useForm<BudgetCreateData>({
    defaultValues,
    resolver: zodResolver(BudgetCreateSchema),
  });

  const isPercentage = form.watch("is_percentage");

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit((data) => createBudget(data))} className="space-y-4">
        {error && <ErrorMessage title="Can't create budget" error={error} />}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Budget Name"
            placeholder="e.g., Groceries, Entertainment"
            className="md:col-span-2"
            disabled={isPending}
            required
          />

          <InputTextarea
            control={form.control}
            name="description"
            label="Description"
            placeholder="Budget description (optional)"
            className="md:col-span-2"
            disabled={isPending}
          />

          <InputSelect
            control={form.control}
            name="period_type"
            values={periodTypeOptions}
            label="Period Type"
            disabled={isPending}
            required
          />

          <InputRadioGroup
            control={form.control}
            name="is_percentage"
            options={percentageOptions}
            label="Budget Type"
            orientation="vertical"
            disabled={isPending}
          />

          <InputText
            control={form.control}
            name="amount"
            label={isPercentage ? "Percentage (%)" : "Amount"}
            placeholder={isPercentage ? "0.00" : "0.00"}
            type="number"
            min={0.01}
            max={isPercentage ? 100 : undefined}
            step="0.01"
            disabled={isPending}
            required
            className="md:col-span-2"
          />
        </div>

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={() => setDialogOpen(false)} disabled={isPending}>
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            {isPending ? "Creating..." : "Create Budget"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
