import type { Budget } from "../types";

import { PlusIcon } from "lucide-react";
import { useShallow } from "zustand/react/shallow";

import { Button } from "~/components/ui/button";

import useBudgetsStore from "../store";
import BudgetsListItem from "./budgets-list-item";

interface Props {
  budgets: Budget[];
}

export default function BudgetsList({ budgets }: Props) {
  const { setDialogOpen, setDialogMode, setCurrentBudget, setInitialValues } = useBudgetsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentBudget: state.setCurrentBudget,
      setInitialValues: state.setInitialValues,
    }))
  );

  const activeBudgets = budgets.filter((budget) => budget.is_active);
  const inactiveBudgets = budgets.filter((budget) => !budget.is_active);

  const handleCreateBudget = () => {
    setCurrentBudget(undefined);
    setInitialValues(undefined);
    setDialogMode("create");
    setDialogOpen(true);
  };

  if (budgets.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="space-y-4">
          <p className="text-muted-foreground">No budgets found</p>
          <Button onClick={handleCreateBudget}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Create Your First Budget
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {activeBudgets.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Active Budgets</h2>
          <div className="space-y-4">
            {activeBudgets.map((budget) => (
              <BudgetsListItem key={budget.id} budget={budget} />
            ))}
          </div>
        </div>
      )}

      {inactiveBudgets.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Inactive Budgets</h2>
          <div className="space-y-4">
            {inactiveBudgets.map((budget) => (
              <BudgetsListItem key={budget.id} budget={budget} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
