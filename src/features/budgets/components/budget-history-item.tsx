import type { BudgetPeriod } from "../types";

import { useMemo } from "react";

import { format } from "date-fns";

import { Progress } from "~/components/ui/progress";

interface Props {
  period: BudgetPeriod;
}

export default function BudgetHistoryItem({ period }: Props) {
  const usagePercentage = useMemo(() => {
    const planned = Number(period.planned_amount);
    const used = Number(period.used_amount);
    
    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [period]);

  const periodStart = format(new Date(period.period_start), "MMM d, yyyy");
  const periodEnd = format(new Date(period.period_end), "MMM d, yyyy");

  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">
            {periodStart} - {periodEnd}
          </h4>
          <span className="text-sm text-muted-foreground">
            ${period.used_amount} / ${period.planned_amount}
          </span>
        </div>

        <div className="space-y-2">
          <Progress value={usagePercentage} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{usagePercentage.toFixed(1)}% used</span>
            <span>
              {usagePercentage > 100 ? "Over budget" : usagePercentage === 100 ? "Budget met" : "Under budget"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
