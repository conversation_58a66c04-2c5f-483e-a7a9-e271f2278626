import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";

import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import { Button } from "~/components/ui/button";
import BudgetDialog from "~/features/budgets/components/budget-dialog";
import BudgetsList from "~/features/budgets/components/budgets-list";
import { useBudgetActions } from "~/features/budgets/hooks/use-budget-actions";
import { useBudgets } from "~/features/budgets/hooks/use-budgets";

export const Route = createFileRoute("/_auth/budgets/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgets, isLoading, error } = useBudgets();
  const { createBudget } = useBudgetActions();

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budgets" />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budgets" />
        <ErrorMessage title="Can't load budgets" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Budgets">
        <Button onClick={() => createBudget()}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Create Budget
        </Button>
      </PageHeader>

      <BudgetsList budgets={budgets} />
      <BudgetDialog />
    </>
  );
}
