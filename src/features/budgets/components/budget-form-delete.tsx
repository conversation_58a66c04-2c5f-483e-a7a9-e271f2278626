import type { Budget } from "../types";

import { useShallow } from "zustand/react/shallow";

import ErrorMessage from "~/components/blocks/error-message";
import { Button } from "~/components/ui/button";

import { useBudgetDelete } from "../hooks";
import useBudgetsStore from "../store";

interface Props {
  budget: Budget;
}

export default function BudgetFormDelete({ budget }: Props) {
  const setDialogOpen = useBudgetsStore(useShallow((state) => state.setDialogOpen));

  const { mutate: deleteBudget, isPending, error } = useBudgetDelete(budget.id);

  return (
    <div className="space-y-4">
      {error && <ErrorMessage title="Can't delete budget" error={error} />}

      <div className="space-y-2">
        <p>Are you sure you want to delete this budget?</p>
        <div className="rounded-md bg-muted p-3">
          <p className="font-medium">{budget.name}</p>
          {budget.description && <p className="text-sm text-muted-foreground">{budget.description}</p>}
        </div>
        <p className="text-sm text-muted-foreground">
          This action cannot be undone. All historical data for this budget will be permanently deleted.
        </p>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={() => setDialogOpen(false)} disabled={isPending}>
          Cancel
        </Button>
        <Button type="button" variant="destructive" onClick={() => deleteBudget()} disabled={isPending}>
          {isPending ? "Deleting..." : "Delete Budget"}
        </Button>
      </div>
    </div>
  );
}
