import { useMemo } from "react";

import { createFileRoute } from "@tanstack/react-router";
import { format } from "date-fns";
import { EditIcon, TrashIcon } from "lucide-react";

import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import { Button } from "~/components/ui/button";
import { Progress } from "~/components/ui/progress";
import BudgetDialog from "~/features/budgets/components/budget-dialog";
import BudgetHistoryList from "~/features/budgets/components/budget-history-list";
import { useBudget } from "~/features/budgets/hooks/use-budget";
import { useBudgetActions } from "~/features/budgets/hooks/use-budget-actions";
import { periodTypesMap } from "~/features/budgets/schemas";

export const Route = createFileRoute("/_auth/budgets/$budgetId")({
  component: RouteComponent,
});

function RouteComponent() {
  const { budgetId } = Route.useParams();

  const { budget, isLoading, error } = useBudget(budgetId);
  const { editBudget, deleteBudget } = useBudgetActions(budget);

  const usagePercentage = useMemo(() => {
    if (!budget) return 0;
    const planned = Number(budget.current_period.planned_amount);
    const used = Number(budget.current_period.used_amount);

    if (planned === 0) return 0;
    return Math.min((used / planned) * 100, 100);
  }, [budget]);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
        <ErrorMessage title="Can't load budget" error={error} />
      </>
    );
  }

  if (!budget) {
    return (
      <>
        <PageHeader title="Budget Details" backLink={{ to: "/budgets" }} />
        <ErrorMessage title="Budget not found" />
      </>
    );
  }

  const periodStart = format(new Date(budget.current_period.period_start), "MMM d, yyyy");
  const periodEnd = format(new Date(budget.current_period.period_end), "MMM d, yyyy");

  return (
    <>
      <PageHeader title={budget.name} backLink={{ to: "/budgets" }}>
        <div className="flex gap-2">
          <Button variant="outline" onClick={editBudget}>
            <EditIcon className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="destructive" onClick={deleteBudget}>
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </PageHeader>

      <div className="space-y-8">
        {/* Budget Overview */}
        <div className="bg-card rounded-lg border p-6">
          <div className="space-y-6">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h2 className="text-xl font-semibold">{budget.name}</h2>
                {budget.description && <p className="text-muted-foreground">{budget.description}</p>}
                <div className="text-muted-foreground flex items-center gap-4 text-sm">
                  <span>{periodTypesMap[budget.period_type]}</span>
                  <span>{budget.is_percentage ? `${budget.amount}%` : `$${budget.amount}`}</span>
                  {!budget.is_active && <span className="bg-muted rounded-full px-2 py-1 text-xs">Inactive</span>}
                </div>
              </div>
            </div>

            {/* Current Period */}
            <div className="space-y-4">
              <h3 className="font-medium">
                Current Period ({periodStart} - {periodEnd})
              </h3>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Usage</span>
                  <span>
                    ${budget.current_period.used_amount} / ${budget.current_period.planned_amount}
                  </span>
                </div>
                <Progress value={usagePercentage} className="h-3" />
                <div className="text-muted-foreground flex justify-between text-xs">
                  <span>{usagePercentage.toFixed(1)}% used</span>
                  <span>
                    {usagePercentage > 100 ? "Over budget" : usagePercentage === 100 ? "Budget met" : "Under budget"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Budget History */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Budget History</h2>
          <BudgetHistoryList budgetId={budgetId} />
        </div>
      </div>

      <BudgetDialog />
    </>
  );
}
